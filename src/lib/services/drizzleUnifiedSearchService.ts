import { ElasticsearchService } from './elasticsearchService';
import { DrizzleFetchService } from './drizzleFetchService';
import { getAllDatabaseCodes } from '@/lib/drizzleTableMapping';
import { getDynamicTable, isDrizzleTable } from '@/lib/drizzleTableMapping';
import { ConfigCacheService } from '@/lib/drizzleConfigCache';
import { getDatabaseConfig } from '@/lib/configCache';
import { db } from '@/lib/db-server';
import { count, desc, asc } from 'drizzle-orm';
import { buildDrizzleWhere, buildDrizzleOrderBy } from '@/lib/server/buildDrizzleWhere';

/**
 * 统一搜索结果接口
 */
export interface UnifiedSearchResult {
  success: boolean;
  data?: {
    results: Array<{
      table_code: string;
      data: unknown[];
      total: number;
      es_total: number;
    }>;
    pagination: {
      page: number;
      limit: number;
      total_pages: number;
      total_results: number;
    };
    search_info: {
      query: string;
      search_time: number;
      es_took: number;
      missing_ids_count: number;
    };
  };
  error?: string;
}

/**
 * 全站搜索结果接口
 */
export interface GlobalSearchResult {
  success: boolean;
  data?: Array<{ database: string; count: number }>;
  search_info?: {
    query: string;
    search_time: number;
    es_took: number;
  };
  error?: string;
}

/**
 * 统一搜索参数接口
 */
export interface UnifiedSearchParams {
  query: string;
  table_code?: string; // 如果指定，则为页内搜索；否则为全站搜索
  filters?: Record<string, unknown>;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

/**
 * Drizzle 统一搜索服务
 * 替代原有的 Prisma 统一搜索服务
 */
export class DrizzleUnifiedSearchService {
  
  /**
   * 全站搜索：按数据库分组统计搜索结果
   * @param query 搜索关键词
   * @returns 每个数据库的匹配数量
   */
  static async globalSearch(query: string): Promise<GlobalSearchResult> {
    const startTime = performance.now();
    
    try {
      console.log('[DrizzleUnifiedSearchService] 全站搜索:', { query });
      
      if (!query.trim()) {
        return {
          success: false,
          data: [],
          search_info: {
            query,
            search_time: 0,
            es_took: 0
          },
          error: '搜索关键词不能为空'
        };
      }
      
      // 获取所有数据库代码
      const databaseCodes = await getAllDatabaseCodes();
      if (databaseCodes.length === 0) {
        return {
          success: false,
          data: [],
          search_info: {
            query,
            search_time: performance.now() - startTime,
            es_took: 0
          },
          error: '没有可搜索的数据库'
        };
      }
      
      // 使用ES按数据库分组搜索
      const esResults = await ElasticsearchService.searchByDatabases(query, databaseCodes);
      
      const searchTime = performance.now() - startTime;
      
      return {
        success: true,
        data: esResults,
        search_info: {
          query,
          search_time: searchTime,
          es_took: 0 // 这里可以从ES结果中获取
        }
      };
      
    } catch (error) {
      console.error('[DrizzleUnifiedSearchService] 全站搜索失败:', error);
      return {
        success: false,
        data: [],
        search_info: {
          query,
          search_time: performance.now() - startTime,
          es_took: 0
        },
        error: error instanceof Error ? error.message : '搜索失败'
      };
    }
  }

  /**
   * 统一搜索：ES搜索 + Drizzle回捞 + 二次筛选排序分页
   * @param params 搜索参数
   * @returns 搜索结果
   */
  static async unifiedSearch(params: UnifiedSearchParams): Promise<UnifiedSearchResult> {
    const {
      query,
      table_code,
      filters = {},
      sort_by,
      sort_order = 'desc',
      page = 1,
      limit = 20
    } = params;

    const startTime = performance.now();

    try {
      console.log('[DrizzleUnifiedSearchService] 统一搜索:', {
        query,
        table_code,
        hasFilters: Object.keys(filters).length > 0,
        page,
        limit
      });

      // 第一步：ES搜索获取ID列表
      let esResult;
      if (table_code) {
        // 页内搜索：单库搜索
        esResult = await ElasticsearchService.search({
          query,
          table_code,
          from: 0,
          size: 10000 // 获取足够多的ID用于后续筛选
        });
      } else {
        // 全站搜索：多库搜索
        const databaseCodes = await getAllDatabaseCodes();
        esResult = await ElasticsearchService.search({
          query,
          table_codes: databaseCodes,
          from: 0,
          size: 10000
        });
      }

      if (!esResult.success) {
        console.warn('[DrizzleUnifiedSearchService] ES搜索失败，尝试直接数据库查询:', esResult.error);

        // 当ES失败时，直接使用Drizzle进行数据库查询（仅支持单库搜索）
        if (table_code) {
          return await this.simpleDrizzleSearch({
            table_code,
            query,
            filters,
            sort_by,
            sort_order,
            page,
            limit
          });
        } else {
          return {
            success: false,
            error: `ES搜索失败且不支持多库直接查询: ${esResult.error}`
          };
        }
      }

      // 第二步：按数据库分组ID
      const idGroups: Array<{ table_code: string; ids: string[]; es_total: number }> = [];
      
      if (table_code) {
        // 页内搜索：单库分组
        idGroups.push({
          table_code,
          ids: esResult.data?.hits?.map((hit: any) => hit._id) || [],
          es_total: esResult.data?.total || 0
        });
      } else {
        // 全站搜索：多库分组
        const groupedHits: Record<string, { ids: string[]; total: number }> = {};
        
        esResult.data?.hits?.forEach((hit: any) => {
          const source = hit._source?.table_code || hit._index;
          if (!groupedHits[source]) {
            groupedHits[source] = { ids: [], total: 0 };
          }
          groupedHits[source].ids.push(hit._id);
          groupedHits[source].total++;
        });

        Object.entries(groupedHits).forEach(([code, data]) => {
          idGroups.push({
            table_code: code,
            ids: data.ids,
            es_total: data.total
          });
        });
      }

      console.log('[DrizzleUnifiedSearchService] ES搜索完成:', {
        totalHits: esResult.data?.total || 0,
        groupCount: idGroups.length,
        took: esResult.data?.took || 0
      });

      // 第三步：Drizzle批量回捞 + 二次筛选排序分页
      let fetchResults;
      if (table_code) {
        // 页内搜索：单库回捞
        const targetGroup = idGroups.find(g => g.table_code === table_code);
        if (!targetGroup) {
          return {
            success: true,
            data: {
              results: [],
              pagination: {
                page,
                limit,
                total_pages: 0,
                total_results: 0
              },
              search_info: {
                query,
                search_time: performance.now() - startTime,
                es_took: esResult.data?.took || 0,
                missing_ids_count: 0
              }
            }
          };
        }
        
        const singleResult = await DrizzleFetchService.singleFetch({
          table_code,
          ids: targetGroup.ids,
          filters,
          sort_by,
          sort_order,
          page,
          limit
        });
        
        fetchResults = [singleResult];
      } else {
        // 全站搜索：多库回捞（不分页，返回每库的前几条）
        fetchResults = await DrizzleFetchService.batchFetch({
          id_groups: idGroups.map(g => ({ table_code: g.table_code, ids: g.ids })),
          filters,
          sort_by,
          sort_order
        });
      }

      // 第四步：构建返回结果
      const results = fetchResults.map(result => {
        const esGroup = idGroups.find(g => g.table_code === result.table_code);
        return {
          table_code: result.table_code,
          data: result.data,
          total: result.total,
          es_total: esGroup?.es_total || 0
        };
      });

      const totalResults = results.reduce((sum, r) => sum + r.total, 0);
      const totalPages = Math.ceil(totalResults / limit);
      const missingIdsCount = fetchResults.reduce((sum, r) => sum + r.missing_ids.length, 0);

      console.log('[DrizzleUnifiedSearchService] 统一搜索完成:', {
        resultCount: results.length,
        totalResults,
        missingIdsCount,
        searchTime: performance.now() - startTime
      });

      return {
        success: true,
        data: {
          results,
          pagination: {
            page,
            limit,
            total_pages: totalPages,
            total_results: totalResults
          },
          search_info: {
            query,
            search_time: performance.now() - startTime,
            es_took: esResult.data?.took || 0,
            missing_ids_count: missingIdsCount
          }
        }
      };

    } catch (error) {
      console.error('[DrizzleUnifiedSearchService] 统一搜索失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '搜索失败'
      };
    }
  }

  /**
   * 简化的Drizzle数据库查询（ES失败时的fallback）
   * @param params 搜索参数
   * @returns 搜索结果
   */
  static async simpleDrizzleSearch(params: UnifiedSearchParams): Promise<UnifiedSearchResult> {
    const {
      table_code,
      query = '',
      filters = {},
      sort_by,
      sort_order = 'desc',
      page = 1,
      limit = 20
    } = params;

    const startTime = performance.now();

    try {
      console.log('[DrizzleUnifiedSearchService] 简化数据库查询:', {
        table_code,
        query,
        hasFilters: Object.keys(filters).length > 0,
        page,
        limit
      });

      if (!table_code) {
        throw new Error('数据库查询需要指定table_code');
      }

      // 获取动态表
      const table = await getDynamicTable(table_code);
      if (!isDrizzleTable(table)) {
        throw new Error(`无效的表对象: ${table_code}`);
      }

      // 获取数据库配置以支持全局搜索
      const config = await getDatabaseConfig(table_code);
      if (!config) {
        throw new Error(`未找到数据库配置: ${table_code}`);
      }

      // 构建where条件
      const whereConditions = [];

      // 处理全局搜索查询
      if (query && query.trim()) {
        const keyword = query.trim();
        const searchableFields = config.fields.filter(f => f.isSearchable && f.searchType === 'contains');

        if (searchableFields.length > 0) {
          const { or, ilike } = require('drizzle-orm');
          const globalSearchConditions = searchableFields
            .map(f => {
              const column = table[f.fieldName];
              return column ? ilike(column, `%${keyword}%`) : null;
            })
            .filter(Boolean);

          if (globalSearchConditions.length > 0) {
            whereConditions.push(or(...globalSearchConditions));
          }
        }
      }

      // 处理筛选条件
      Object.entries(filters).forEach(([key, value]) => {
        if (!value || (Array.isArray(value) && value.length === 0)) return;

        const column = table[key];
        if (!column) return;

        if (Array.isArray(value)) {
          // 多选字段处理
          const { inArray, or, isNull, eq } = require('drizzle-orm');
          const hasNullValue = value.includes('N/A');
          if (hasNullValue) {
            const nonNullValues = value.filter(v => v !== 'N/A');
            if (nonNullValues.length > 0) {
              whereConditions.push(
                or(
                  inArray(column, nonNullValues),
                  isNull(column),
                  eq(column, '')
                )
              );
            } else {
              whereConditions.push(
                or(
                  isNull(column),
                  eq(column, '')
                )
              );
            }
          } else {
            whereConditions.push(inArray(column, value));
          }
        } else if (value === 'N/A') {
          const { or, isNull, eq } = require('drizzle-orm');
          whereConditions.push(
            or(
              isNull(column),
              eq(column, '')
            )
          );
        } else {
          const { eq } = require('drizzle-orm');
          whereConditions.push(eq(column, value));
        }
      });

      // 组合where条件
      let finalWhere;
      if (whereConditions.length === 0) {
        finalWhere = undefined;
      } else if (whereConditions.length === 1) {
        finalWhere = whereConditions[0];
      } else {
        const { and } = require('drizzle-orm');
        finalWhere = and(...whereConditions);
      }

      // 计算总数
      const totalCountResult = await db
        .select({ count: count() })
        .from(table)
        .where(finalWhere);

      const totalCount = Number(totalCountResult[0]?.count || 0);

      // 查询数据
      const offset = (page - 1) * limit;

      // 构建排序（简化版本）
      let orderByClause = [desc(table.id)]; // 默认按id降序
      if (sort_by && table[sort_by]) {
        const column = table[sort_by];
        orderByClause = sort_order === 'asc' ? [asc(column)] : [desc(column)];
      }

      let queryBuilder = db
        .select()
        .from(table);

      if (finalWhere) {
        queryBuilder = queryBuilder.where(finalWhere);
      }

      const data = await queryBuilder
        .orderBy(...orderByClause)
        .limit(limit)
        .offset(offset);

      const searchTime = performance.now() - startTime;

      return {
        success: true,
        data: {
          results: [{
            table_code,
            data,
            total: totalCount,
            es_total: 0 // 直接查询没有ES统计
          }],
          pagination: {
            page,
            limit,
            total_pages: Math.ceil(totalCount / limit),
            total_results: totalCount
          },
          search_info: {
            query,
            search_time: searchTime,
            es_took: 0,
            missing_ids_count: 0
          }
        }
      };

    } catch (error) {
      console.error('[DrizzleUnifiedSearchService] 简化数据库查询失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '数据库查询失败'
      };
    }
  }

  /**
   * 获取表列信息（从DrizzleFetchService复制）
   */
  private static getTableColumns(table: any): Record<string, any> {
    // 这里需要根据实际的 Drizzle 表结构来获取列信息
    // 暂时返回表本身，因为 Drizzle 表对象包含了列信息
    return table;
  }

  /**
   * 构建排序子句（从DrizzleFetchService复制）
   */
  private static buildOrderByClause(orderByConditions: any[], tableColumns: Record<string, any>): any[] {
    if (!orderByConditions || orderByConditions.length === 0) {
      // 默认按 id 降序排序
      return [desc(tableColumns.id)];
    }

    return orderByConditions.map(condition => {
      const [field, direction] = Object.entries(condition)[0] as [string, string];
      const column = tableColumns[field];
      if (!column) return null;

      return direction === 'desc' ? desc(column) : asc(column);
    }).filter(Boolean);
  }
}

// 向后兼容的导出
export const UnifiedSearchService = DrizzleUnifiedSearchService;
