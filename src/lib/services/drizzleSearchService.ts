/**
 * 完全基于Drizzle的统一搜索服务
 * 替代原有的Prisma搜索逻辑
 */

import { eq, and, or, like, ilike, gte, lte, inArray, sql, count, desc, asc } from 'drizzle-orm';
import { getDatabaseInstance, serverConfigStore, ensureInitialized } from '../database-servers';
import { ElasticsearchService } from './elasticsearchService';

// 搜索条件接口
export interface SearchCondition {
  id: string;
  field: string;
  value: string | string[] | { from?: string; to?: string };
  logic?: 'AND' | 'OR' | 'NOT';
}

// 搜索参数接口
export interface DrizzleSearchParams {
  database: string;
  conditions?: SearchCondition[];
  globalKeyword?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 搜索结果接口
export interface DrizzleSearchResult {
  success: boolean;
  data: any[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  searchInfo: {
    database: string;
    conditions: SearchCondition[];
    globalKeyword?: string;
    searchTime: number;
    esSearchTime?: number;
  };
  error?: string;
}

// 字段统计结果接口
export interface FieldStatistics {
  field: string;
  items: Array<{
    value: string;
    count: number;
  }>;
  total: number;
}

/**
 * Drizzle统一搜索服务
 */
export class DrizzleSearchService {
  
  /**
   * 执行统一搜索
   */
  static async search(params: DrizzleSearchParams): Promise<DrizzleSearchResult> {
    const startTime = performance.now();
    const {
      database,
      conditions = [],
      globalKeyword,
      page = 1,
      limit = 20,
      sortBy,
      sortOrder = 'desc'
    } = params;

    try {
      // 确保数据库服务器配置已初始化
      await ensureInitialized();

      // 获取数据库实例
      const db = getDatabaseInstance(database);
      const mapping = serverConfigStore.getMapping(database);
      const tableName = mapping?.tableName || database;

      // 如果有全局关键词，先使用ES搜索获取ID列表
      let esIds: string[] | undefined;
      let esSearchTime = 0;
      
      if (globalKeyword && globalKeyword.trim()) {
        const esStartTime = performance.now();
        try {
          const esResult = await ElasticsearchService.search(globalKeyword, [database]);
          esIds = esResult.hits.map(hit => hit.id);
          esSearchTime = performance.now() - esStartTime;
        } catch (error) {
          console.warn('[DrizzleSearchService] ES搜索失败，回退到数据库搜索:', error);
        }
      }

      // 构建Drizzle查询
      const table = this.getTableSchema(database);
      if (!table) {
        throw new Error(`Unsupported database: ${database}`);
      }

      // 构建WHERE条件
      const whereConditions = this.buildWhereConditions(table, conditions, esIds);
      
      // 构建排序
      const orderBy = this.buildOrderBy(table, sortBy, sortOrder);

      // 执行查询
      const offset = (page - 1) * limit;
      
      // 查询数据
      const query = db.select().from(table);
      if (whereConditions.length > 0) {
        query.where(and(...whereConditions));
      }
      if (orderBy) {
        query.orderBy(orderBy);
      }
      query.limit(limit).offset(offset);

      const data = await query;

      // 查询总数
      const countQuery = db.select({ count: count() }).from(table);
      if (whereConditions.length > 0) {
        countQuery.where(and(...whereConditions));
      }
      const [{ count: total }] = await countQuery;

      const searchTime = performance.now() - startTime;

      return {
        success: true,
        data,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        },
        searchInfo: {
          database,
          conditions,
          globalKeyword,
          searchTime,
          esSearchTime: esSearchTime > 0 ? esSearchTime : undefined
        }
      };

    } catch (error) {
      console.error('[DrizzleSearchService] 搜索失败:', error);
      
      return {
        success: false,
        data: [],
        pagination: { page, limit, total: 0, totalPages: 0 },
        searchInfo: {
          database,
          conditions,
          globalKeyword,
          searchTime: performance.now() - startTime
        },
        error: error instanceof Error ? error.message : '搜索失败'
      };
    }
  }

  /**
   * 获取字段统计信息
   */
  static async getFieldStatistics(
    database: string,
    fieldName: string,
    conditions: SearchCondition[] = [],
    limit: number = 50
  ): Promise<FieldStatistics> {
    try {
      // 确保数据库服务器配置已初始化
      await ensureInitialized();

      const db = getDatabaseInstance(database);
      const table = this.getTableSchema(database);
      
      if (!table || !table[fieldName]) {
        throw new Error(`Field ${fieldName} not found in table ${database}`);
      }

      const field = table[fieldName];
      const whereConditions = this.buildWhereConditions(table, conditions);

      // 构建统计查询
      const query = db
        .select({
          value: field,
          count: count()
        })
        .from(table)
        .groupBy(field)
        .orderBy(desc(count()))
        .limit(limit);

      if (whereConditions.length > 0) {
        query.where(and(...whereConditions));
      }

      const results = await query;
      
      // 计算总数
      const totalQuery = db.select({ count: count() }).from(table);
      if (whereConditions.length > 0) {
        totalQuery.where(and(...whereConditions));
      }
      const [{ count: total }] = await totalQuery;

      return {
        field: fieldName,
        items: results.map(r => ({
          value: String(r.value || ''),
          count: r.count
        })),
        total
      };

    } catch (error) {
      console.error('[DrizzleSearchService] 获取字段统计失败:', error);
      return {
        field: fieldName,
        items: [],
        total: 0
      };
    }
  }

  /**
   * 获取表Schema
   */
  private static getTableSchema(database: string) {
    // 这里需要根据数据库代码返回对应的表schema
    // 将来可以从配置中动态获取
    const { usPremarketNotifications, usClass } = require('../../db/schema');
    
    switch (database) {
      case 'us_pmn':
        return usPremarketNotifications;
      case 'us_class':
        return usClass;
      default:
        return null;
    }
  }

  /**
   * 构建WHERE条件
   */
  private static buildWhereConditions(table: any, conditions: SearchCondition[], esIds?: string[]) {
    const whereConditions = [];

    // 如果有ES搜索结果，添加ID限制
    if (esIds && esIds.length > 0 && table.id) {
      whereConditions.push(inArray(table.id, esIds));
    }

    // 处理搜索条件
    conditions.forEach(condition => {
      const field = table[condition.field];
      if (!field) return;

      const { value } = condition;

      if (typeof value === 'string') {
        if (value.trim()) {
          whereConditions.push(ilike(field, `%${value.trim()}%`));
        }
      } else if (Array.isArray(value)) {
        if (value.length > 0) {
          whereConditions.push(inArray(field, value));
        }
      } else if (typeof value === 'object' && value !== null) {
        const { from, to } = value;
        if (from) {
          whereConditions.push(gte(field, from));
        }
        if (to) {
          whereConditions.push(lte(field, to));
        }
      }
    });

    return whereConditions;
  }

  /**
   * 构建排序
   */
  private static buildOrderBy(table: any, sortBy?: string, sortOrder: 'asc' | 'desc' = 'desc') {
    if (!sortBy || !table[sortBy]) {
      // 默认按ID排序
      return sortOrder === 'asc' ? asc(table.id) : desc(table.id);
    }

    const field = table[sortBy];
    return sortOrder === 'asc' ? asc(field) : desc(field);
  }
}
