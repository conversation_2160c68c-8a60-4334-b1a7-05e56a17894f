import { db } from '@/lib/db-server';
import { getDynamicTable, isDrizzleTable } from '@/lib/drizzleTableMapping';
import { getDatabaseConfig } from '@/lib/configCache';
import { buildDrizzleWhere, buildDrizzleOrderBy } from '@/lib/server/buildDrizzleWhere';
import { inArray, count, asc, desc } from 'drizzle-orm';
import type { DatabaseConfig } from '@/lib/drizzleConfigCache';

/**
 * Drizzle 回捞结果接口
 */
export interface DrizzleFetchResult {
  table_code: string;
  data: unknown[];
  total: number;
  missing_ids: string[]; // ES有但DB无的ID
}

/**
 * 批量回捞参数接口
 */
export interface BatchFetchParams {
  id_groups: Array<{
    table_code: string;
    ids: string[];
  }>;
  filters?: Record<string, unknown>;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

/**
 * 单库回捞参数接口
 */
export interface SingleFetchParams {
  table_code: string;
  ids: string[];
  filters?: Record<string, unknown>;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

/**
 * Drizzle 回捞服务
 * 基于ES返回的ID集合批量查询Drizzle，支持二次筛选和排序分页
 */
export class DrizzleFetchService {
  
  /**
   * 批量回捞多个数据库的数据
   * @param params 批量回捞参数
   * @returns 按数据库分组的回捞结果
   */
  static async batchFetch(params: BatchFetchParams): Promise<DrizzleFetchResult[]> {
    const { id_groups, filters = {}, sort_by, sort_order = 'desc' } = params;
    
    console.log('[DrizzleFetchService] 批量回捞:', { 
      groupCount: id_groups.length, 
      totalIds: id_groups.reduce((sum, group) => sum + group.ids.length, 0)
    });
    
    const results: DrizzleFetchResult[] = [];
    
    // 并行处理每个数据库
    const fetchPromises = id_groups.map(async (group) => {
      try {
        const result = await this.singleFetch({
          table_code: group.table_code,
          ids: group.ids,
          filters,
          sort_by,
          sort_order
        });
        
        return result;
      } catch (error) {
        console.error(`[DrizzleFetchService] 数据库 ${group.table_code} 回捞失败:`, error);
        
        // 返回空结果，但记录所有ID为缺失
        return {
          table_code: group.table_code,
          data: [],
          total: 0,
          missing_ids: group.ids
        };
      }
    });
    
    const fetchResults = await Promise.all(fetchPromises);
    results.push(...fetchResults);
    
    console.log('[DrizzleFetchService] 批量回捞完成:', {
      resultCount: results.length,
      totalData: results.reduce((sum, r) => sum + r.data.length, 0),
      totalMissing: results.reduce((sum, r) => sum + r.missing_ids.length, 0)
    });
    
    return results;
  }

  /**
   * 单库回捞数据
   * @param params 单库回捞参数
   * @returns 回捞结果
   */
  static async singleFetch(params: SingleFetchParams): Promise<DrizzleFetchResult> {
    const { 
      table_code, 
      ids, 
      filters = {}, 
      sort_by, 
      sort_order = 'desc',
      page = 1,
      limit = 20
    } = params;

    console.log(`[DrizzleFetchService] 单库回捞 ${table_code}:`, {
      idsCount: ids.length,
      hasFilters: Object.keys(filters).length > 0,
      sort_by,
      sort_order,
      page,
      limit
    });

    try {
      // 获取动态表和配置
      const table = await getDynamicTable(table_code);
      if (!isDrizzleTable(table)) {
        throw new Error(`无效的表对象: ${table_code}`);
      }

      const config = await getDatabaseConfig(table_code);
      
      // 构建基础查询条件：ID 在指定列表中
      const baseCondition = inArray(table.id, ids);
      
      // 构建额外的筛选条件
      const searchParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => searchParams.append(key, String(v)));
        } else {
          searchParams.set(key, String(value));
        }
      });

      // 获取表列信息（这里需要根据实际的 Drizzle 表结构来获取）
      const tableColumns = this.getTableColumns(table);
      
      // 构建 where 条件
      const additionalWhere = buildDrizzleWhere(searchParams, config, tableColumns);
      const finalWhere = additionalWhere ? [baseCondition, additionalWhere] : [baseCondition];

      // 构建排序条件
      const orderByConditions = buildDrizzleOrderBy(sort_by, sort_order, tableColumns, config);

      // 执行查询
      const offset = (page - 1) * limit;
      
      // 查询数据
      const data = await db
        .select()
        .from(table)
        .where(finalWhere.length > 1 ? finalWhere : finalWhere[0])
        .orderBy(...this.buildOrderByClause(orderByConditions, tableColumns))
        .limit(limit)
        .offset(offset);

      // 查询总数
      const totalResult = await db
        .select({ count: count() })
        .from(table)
        .where(finalWhere.length > 1 ? finalWhere : finalWhere[0]);
      
      const total = totalResult[0]?.count || 0;

      // 找出缺失的ID
      const foundIds = new Set(data.map((item: any) => item.id));
      const missing_ids = ids.filter(id => !foundIds.has(id));

      console.log(`[DrizzleFetchService] ${table_code} 回捞完成:`, {
        found: data.length,
        total,
        missing: missing_ids.length
      });

      return {
        table_code,
        data,
        total,
        missing_ids
      };

    } catch (error) {
      console.error(`[DrizzleFetchService] ${table_code} 回捞失败:`, error);
      throw error;
    }
  }

  /**
   * 获取表列信息
   * @param table Drizzle 表对象
   * @returns 表列映射
   */
  private static getTableColumns(table: any): Record<string, any> {
    // 这里需要根据 Drizzle 表的实际结构来提取列信息
    // 暂时返回一个基础的映射，后续需要完善
    const columns: Record<string, any> = {};
    
    if (table && table._) {
      // 从 Drizzle 表对象中提取列信息
      Object.keys(table).forEach(key => {
        if (key !== '_' && table[key]) {
          columns[key] = table[key];
        }
      });
    }
    
    return columns;
  }

  /**
   * 构建 Drizzle 排序子句
   * @param orderByConditions 排序条件
   * @param tableColumns 表列
   * @returns 排序子句数组
   */
  private static buildOrderByClause(
    orderByConditions: any[], 
    tableColumns: Record<string, any>
  ): any[] {
    return orderByConditions.map(condition => {
      const [field, direction] = Object.entries(condition)[0] as [string, string];
      const column = tableColumns[field];
      if (!column) return null;
      
      return direction === 'desc' ? desc(column) : asc(column);
    }).filter(Boolean);
  }
}

// 向后兼容的导出
export const PrismaFetchService = DrizzleFetchService;
