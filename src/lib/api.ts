import { DATABASE_CONFIGS } from './permissions-client';
import type { DatabaseConfig } from './configCache';

// API 响应类型定义
export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
    // 翻页限制信息
    maxPages: number;
    isAtMaxPages: boolean;
    maxPageSize: number;
    defaultPageSize: number;
  };
  filters?: Record<string, unknown>;
  config?: DatabaseConfig;
  error?: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
}

// 通用数据记录
export type DataRecord = Record<string, unknown>;

// 为保持向后兼容性或特定页面类型安全而保留的类型
export interface MedicalDevice extends DataRecord {
  id: number;
  productName: string;
  companyName: string;
}

// 通用的搜索函数
export const searchData = async (
  database: string,
  page: number,
  limit: number,
  filters: Record<string, any>,
  sortBy: string,
  sortOrder: string
): Promise<PaginatedResponse<DataRecord>> => {
  if (!database) {
    throw new Error("Database parameter is required for searching.");
  }

  // 提取 allFields 参数，因为 API 期望它作为单独的查询参数
  const { allFields, ...otherFilters } = filters;

  const searchParams: Record<string, string> = {
    page: String(page),
    limit: String(limit),
    sortBy,
    sortOrder,
    filters: JSON.stringify(otherFilters),
  };

  // 如果有 allFields，添加为单独的查询参数
  if (allFields && typeof allFields === 'string' && allFields.trim()) {
    searchParams.allFields = allFields.trim();
  }

  const queryString = new URLSearchParams(searchParams).toString();

  // 使用统一搜索API
  const res = await fetch(`/api/advanced-search/${database}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      conditions: convertFiltersToConditions(otherFilters),
      page: parseInt(searchParams.page) || 1,
      limit: parseInt(searchParams.limit) || 20,
      sortBy: searchParams.sortBy,
      sortOrder: searchParams.sortOrder || 'desc',
      globalKeyword: allFields || undefined
    }),
  });

  if (!res.ok) {
    const errorBody = await res.json().catch(() => ({ error: 'Failed to fetch data' }));
    throw new Error(errorBody.error || 'Network response was not ok');
  }
  return res.json();
};

/**
 * 根据ID获取单个数据记录
 * @param database 数据库代码
 * @param id 记录ID
 * @returns 单个数据记录
 */
export const getDataById = async (
  database: string,
  id: number
): Promise<ApiResponse<DataRecord>> => {
  if (!database || !id) {
    throw new Error("Database and ID parameters are required.");
  }

  const res = await fetch(`/api/data/${database}/${id}`);

  if (!res.ok) {
    const errorBody = await res.json().catch(() => ({ error: 'Failed to fetch item details' }));
    throw new Error(errorBody.error || 'Network response was not ok');
  }
  return res.json();
};

/**
 * @deprecated 旧版函数，请直接使用 searchData。此函数为兼容旧代码而保留。
 * 调用通用的 searchData 函数，但将返回类型断言为 MedicalDevice 数组。
 */
export const searchDevices = async (
  params: Record<string, string | number | boolean> & { database: string }
): Promise<PaginatedResponse<MedicalDevice>> => {
  const { database, page = 1, limit = 20, ...filters } = params;
  const sortBy = String(params.sortBy || 'id');
  const sortOrder = String(params.sortOrder || 'desc');
  return searchData(database, Number(page), Number(limit), filters, sortBy, sortOrder) as Promise<PaginatedResponse<MedicalDevice>>;
};

/**
 * 首页等场景下的全局搜索便捷函数
 * @param query 搜索关键词
 * @param database 目标数据库
 * @returns 分页后的数据
 */
export const globalSearch = async (query: string, database = 'deviceCNImported') => {
  const filters = { productName: query };
  return searchData(database, 1, 20, filters, 'id', 'desc');
};

// 高级搜索接口
export interface SearchCondition {
  id: string;
  field: string;
  value: string | string[] | { from?: string; to?: string };
  logic?: 'AND' | 'OR' | 'NOT';
}

export interface AdvancedSearchParams {
  database: string;
  conditions: SearchCondition[];
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * 将传统的filters对象转换为SearchCondition数组
 */
function convertFiltersToConditions(filters: Record<string, any>): SearchCondition[] {
  const conditions: SearchCondition[] = [];

  Object.entries(filters).forEach(([fieldName, value], index) => {
    if (value === undefined || value === null || value === '') return;

    const condition: SearchCondition = {
      id: `filter_${fieldName}_${Date.now()}_${index}`,
      field: fieldName,
      value,
      logic: index > 0 ? 'AND' : undefined,
    };

    conditions.push(condition);
  });

  return conditions;
}

// 统一搜索接口 - 同时支持高级搜索条件和简单过滤器
export interface UnifiedSearchParams {
  database: string;
  // 高级搜索条件
  advancedConditions?: SearchCondition[];
  // 简单过滤器（来自左侧面板）
  filters?: Record<string, unknown>;
  // 分页和排序
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * 高级搜索函数
 * @param params 高级搜索参数
 * @returns 分页后的数据
 */
export const advancedSearch = async (params: AdvancedSearchParams): Promise<PaginatedResponse<DataRecord>> => {
  const { database, ...searchParams } = params;
  if (!database) {
    throw new Error("Database parameter is required for advanced search.");
  }

  const res = await fetch(`/api/advanced-search/${database}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(searchParams),
  });

  if (!res.ok) {
    const errorBody = await res.json().catch(() => ({ error: 'Failed to perform advanced search' }));
    throw new Error(errorBody.error || 'Network response was not ok');
  }
  return res.json();
};

/**
 * 统一搜索函数 - 同时支持高级搜索条件和简单过滤器
 * @param params 统一搜索参数
 * @returns 分页后的数据
 */
export const unifiedSearch = async (params: UnifiedSearchParams): Promise<PaginatedResponse<DataRecord>> => {
  const { database, advancedConditions, filters, page, limit, sortBy, sortOrder } = params;
  if (!database) {
    throw new Error("Database parameter is required for unified search.");
  }

  // Convert parameters to the format expected by the API
  const requestBody = {
    advancedConditions: advancedConditions || [],
    filters: filters || {},
    page: page || 1,
    limit: limit || 20,
    sortBy: sortBy,
    sortOrder: sortOrder || 'desc'
  };

  const res = await fetch(`/api/unified-search/${database}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(requestBody),
  });

  if (!res.ok) {
    const errorBody = await res.json().catch(() => ({ error: 'Failed to perform unified search' }));
    throw new Error(errorBody.error || 'Network response was not ok');
  }
  return res.json();
};

/**
 * 获取所有已配置的数据库列表
 * @returns 数据库配置列表
 */
export const getDatabases = () => {
  return Object.entries(DATABASE_CONFIGS).map(([code, config]: [string, any]) => ({
    code,
    name: config.name,
    description: config.description,
  }));
};
