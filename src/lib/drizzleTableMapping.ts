import { db } from './db-server';
import { 
  databaseConfigs, 
  fieldConfigs, 
  users, 
  userSessions, 
  companies, 
  activityLogs, 
  searchAnalytics, 
  blockedIps, 
  dataChangeLogs, 
  contactSubmissions,
  usPremarketNotifications,
  usClasses
} from '../db/schema';
import { eq } from 'drizzle-orm';

/**
 * 数据库代码到模型映射的接口定义
 */
export interface DrizzleTableMapping {
  table: any; // Drizzle 表对象
  tableName: string;
  displayName: string;
  category: string;
  description: string;
}

/**
 * 静态表映射 - Drizzle 版本
 * 将数据库代码映射到对应的 Drizzle 表对象
 */
const DRIZZLE_TABLE_MAPPING: Record<string, any> = {
  // 用户管理
  'users': users,
  'userSessions': userSessions,
  
  // 配置管理
  'databaseConfigs': databaseConfigs,
  'fieldConfigs': fieldConfigs,
  
  // 公司数据
  'companies': companies,
  
  // 日志和分析
  'activityLogs': activityLogs,
  'searchAnalytics': searchAnalytics,
  'blockedIps': blockedIps,
  'dataChangeLogs': dataChangeLogs,
  'contactSubmissions': contactSubmissions,
  
  // 医疗器械数据
  'us_pmn': usPremarketNotifications,
  'us_class': usClasses,
  
  // 别名映射 - 兼容现有代码
  'USPremarketNotification': usPremarketNotifications,
  'uSPremarketNotification': usPremarketNotifications, // 添加缺失的映射
  'usPremarketNotifications': usPremarketNotifications, // 复数形式
  'medicalDevice_US_PMN': usPremarketNotifications, // 旧的模型名
  'USClass': usClasses,
  'uSClass': usClasses, // 添加缺失的映射
  'User': users,
  'UserSession': userSessions,
  'DatabaseConfig': databaseConfigs,
  'FieldConfig': fieldConfigs,
  'Company': companies,
  'ActivityLog': activityLogs,
  'SearchAnalytics': searchAnalytics,
  'BlockedIp': blockedIps,
  'DataChangeLog': dataChangeLogs,
  'ContactSubmission': contactSubmissions,
};

/**
 * Drizzle 动态表映射服务
 * 替代 Prisma 的动态模型访问
 */
export class DrizzleTableMappingService {
  private static configCache: Record<string, DrizzleTableMapping> = {};
  private static cacheExpiry = 0;
  private static readonly CACHE_TTL = 300000; // 5分钟缓存

  /**
   * 从数据库获取表映射配置
   * @param databaseCode 数据库代码
   * @returns 映射配置对象
   */
  static async getTableMapping(databaseCode: string): Promise<DrizzleTableMapping | null> {
    // 检查缓存
    if (this.configCache[databaseCode] && Date.now() < this.cacheExpiry) {
      return this.configCache[databaseCode];
    }

    try {
      // 从数据库读取配置
      const config = await db
        .select({
          code: databaseConfigs.code,
          name: databaseConfigs.name,
          category: databaseConfigs.category,
          description: databaseConfigs.description,
          tableName: databaseConfigs.tableName,
          modelName: databaseConfigs.modelName,
        })
        .from(databaseConfigs)
        .where(eq(databaseConfigs.code, databaseCode))
        .limit(1);

      if (!config.length || !config[0].tableName || !config[0].modelName) {
        console.warn(`[DrizzleTableMappingService] 数据库代码 ${databaseCode} 的表映射配置不完整或不存在`);
        return null;
      }

      const configData = config[0];

      // 获取对应的 Drizzle 表对象
      const table = DRIZZLE_TABLE_MAPPING[configData.modelName];

      if (!table) {
        console.error(`[DrizzleTableMappingService] 找不到 Drizzle 表: ${configData.modelName}`);
        console.error(`[DrizzleTableMappingService] 尝试查找的键: "${configData.modelName}"`);
        console.error(`[DrizzleTableMappingService] 可用的键: ${Object.keys(DRIZZLE_TABLE_MAPPING).join(', ')}`);
        return null;
      }

      const mapping: DrizzleTableMapping = {
        table,
        tableName: configData.tableName,
        displayName: configData.name,
        category: configData.category,
        description: configData.description || ''
      };

      // 更新缓存
      this.configCache[databaseCode] = mapping;
      this.cacheExpiry = Date.now() + this.CACHE_TTL;

      return mapping;
    } catch (error) {
      console.error(`[DrizzleTableMappingService] 获取表映射配置失败 ${databaseCode}:`, error);
      return null;
    }
  }

  /**
   * 动态获取 Drizzle 表对象
   * @param databaseCode 数据库代码
   * @returns Drizzle 表对象
   */
  static async getDynamicTable(databaseCode: string) {
    const mapping = await this.getTableMapping(databaseCode);
    if (!mapping) {
      throw new Error(`不支持的数据库代码: ${databaseCode}. 请检查DatabaseConfig表中的配置`);
    }

    return mapping.table;
  }

  /**
   * 直接通过模型名获取表对象（用于向后兼容）
   * @param modelName 模型名
   * @returns Drizzle 表对象
   */
  static getTableByModelName(modelName: string) {
    const table = DRIZZLE_TABLE_MAPPING[modelName];
    if (!table) {
      throw new Error(`找不到 Drizzle 表: ${modelName}. 请检查表映射配置`);
    }
    return table;
  }

  /**
   * 获取所有可用的数据库代码
   * @returns 数据库代码数组
   */
  static async getAllDatabaseCodes(): Promise<string[]> {
    try {
      const configs = await db
        .select({ code: databaseConfigs.code })
        .from(databaseConfigs)
        .where(eq(databaseConfigs.isActive, true));

      return configs.map(config => config.code);
    } catch (error) {
      console.error(`[DrizzleTableMappingService] 获取数据库代码失败:`, error);
      return [];
    }
  }

  /**
   * 检查表对象是否有效
   * @param table Drizzle 表对象
   * @returns 是否为有效的 Drizzle 表
   */
  static isDrizzleTable(table: unknown): boolean {
    if (!table || typeof table !== 'object') {
      return false;
    }

    // 检查是否是 Drizzle 表对象
    // Drizzle 表对象的原型应该是 Table 或者包含表的特征
    const prototype = Object.getPrototypeOf(table);
    const isTable = prototype && prototype.constructor && prototype.constructor.name === 'Table';

    // 或者检查是否有典型的 Drizzle 表属性
    const hasTableProperties = 'enableRLS' in table || Object.keys(table).length > 0;

    return isTable || hasTableProperties;
  }

  /**
   * 清除缓存
   */
  static clearCache(): void {
    this.configCache = {};
    this.cacheExpiry = 0;
  }
}

// 向后兼容的导出函数
export async function getDynamicTable(databaseCode: string) {
  return DrizzleTableMappingService.getDynamicTable(databaseCode);
}

export async function getTableMapping(databaseCode: string): Promise<DrizzleTableMapping | null> {
  return DrizzleTableMappingService.getTableMapping(databaseCode);
}

export async function getAllDatabaseCodes(): Promise<string[]> {
  return DrizzleTableMappingService.getAllDatabaseCodes();
}

export function isDrizzleTable(table: unknown): boolean {
  return DrizzleTableMappingService.isDrizzleTable(table);
}

export function validateDatabaseCode(databaseCode: string): { isValid: boolean; error?: string; status?: number } {
  if (!databaseCode || typeof databaseCode !== 'string') {
    return {
      isValid: false,
      error: '数据库代码不能为空',
      status: 400
    };
  }

  // 基本格式验证
  if (!/^[a-zA-Z0-9_-]+$/.test(databaseCode)) {
    return {
      isValid: false,
      error: '数据库代码格式无效',
      status: 400
    };
  }

  return { isValid: true };
}
