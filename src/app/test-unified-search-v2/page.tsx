'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Search, Filter, BarChart3 } from 'lucide-react';
import { useUnifiedSearch, UnifiedSearchParams } from '@/lib/hooks/useUnifiedSearch';
import { SearchCondition } from '@/lib/api';

export default function TestUnifiedSearchV2() {
  const [database] = useState('us_pmn');
  const [config, setConfig] = useState<any>(null);
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 测试状态
  const [filterPanelFilters, setFilterPanelFilters] = useState<Record<string, any>>({});
  const [advancedSearchConditions, setAdvancedSearchConditions] = useState<SearchCondition[]>([]);
  const [globalKeyword, setGlobalKeyword] = useState('');

  // 使用统一搜索Hook
  const { 
    searchState, 
    executeSearch, 
    updateFromFilterPanel, 
    updateFromAdvancedSearch,
    getFieldStatistics,
    clearAllConditions
  } = useUnifiedSearch(database, config);

  // 加载配置
  useEffect(() => {
    const loadConfig = async () => {
      try {
        // 通过API获取配置，避免在客户端直接调用服务器端模块
        const response = await fetch(`/api/config/${database}`);
        const result = await response.json();

        if (result.success) {
          setConfig(result.data);
          addTestResult(`✅ 配置加载成功: ${result.data.fields.length} 个字段`);
        } else {
          addTestResult(`❌ 配置加载失败: ${result.error}`);
        }
      } catch (error) {
        addTestResult(`❌ 配置加载失败: ${error}`);
      }
    };

    loadConfig();
  }, [database]);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  // 测试Filter面板搜索
  const testFilterPanelSearch = async () => {
    setIsLoading(true);
    addTestResult('🔧 测试Filter面板搜索...');

    try {
      const testFilters = {
        decision: ['Substantially Equivalent'],
        productcode: ['LRH', 'MHX']
      };

      setFilterPanelFilters(testFilters);
      const result = await updateFromFilterPanel(testFilters);

      if (result?.success) {
        addTestResult(`✅ Filter面板搜索成功: ${result.data?.length || 0} 条结果`);
        addTestResult(`📊 统计缓存: ${searchState.statisticsCache.size} 个字段`);
      } else {
        addTestResult(`❌ Filter面板搜索失败: ${result?.error}`);
      }
    } catch (error) {
      addTestResult(`❌ Filter面板搜索异常: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 测试Advanced Search
  const testAdvancedSearch = async () => {
    setIsLoading(true);
    addTestResult('🔧 测试Advanced Search...');

    try {
      const testConditions: SearchCondition[] = [
        {
          id: 'cond_1',
          field: 'applicant',
          value: 'Abbott',
          logic: undefined
        },
        {
          id: 'cond_2',
          field: 'decision',
          value: 'Substantially Equivalent',
          logic: 'AND'
        }
      ];

      setAdvancedSearchConditions(testConditions);
      const result = await updateFromAdvancedSearch(testConditions);

      if (result?.success) {
        addTestResult(`✅ Advanced Search成功: ${result.data?.length || 0} 条结果`);
        addTestResult(`📊 统计缓存: ${searchState.statisticsCache.size} 个字段`);
      } else {
        addTestResult(`❌ Advanced Search失败: ${result?.error}`);
      }
    } catch (error) {
      addTestResult(`❌ Advanced Search异常: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 测试全局搜索
  const testGlobalSearch = async () => {
    setIsLoading(true);
    addTestResult('🔧 测试全局搜索...');

    try {
      const keyword = 'dental';
      setGlobalKeyword(keyword);

      const result = await executeSearch({
        globalKeyword: keyword,
        conditions: [],
        page: 1,
        limit: 20
      });

      if (result?.success) {
        addTestResult(`✅ 全局搜索成功: ${result.data?.length || 0} 条结果`);
      } else {
        addTestResult(`❌ 全局搜索失败: ${result?.error}`);
      }
    } catch (error) {
      addTestResult(`❌ 全局搜索异常: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 测试统计数据共享
  const testStatisticsSharing = async () => {
    addTestResult('🔧 测试统计数据共享...');

    if (!config) {
      addTestResult('❌ 配置未加载，无法测试统计');
      return;
    }

    const statisticsFields = config.fields.filter((f: any) => f.isStatisticsEnabled);
    addTestResult(`📊 启用统计的字段: ${statisticsFields.length} 个`);

    statisticsFields.slice(0, 3).forEach((field: any) => {
      const stats = getFieldStatistics(field.fieldName);
      if (stats) {
        addTestResult(`  - ${field.displayName}: ${stats.items.length} 个选项, 总计 ${stats.total}`);
      } else {
        addTestResult(`  - ${field.displayName}: 无统计数据`);
      }
    });
  };

  // 清空测试
  const testClearAll = async () => {
    setIsLoading(true);
    addTestResult('🔧 测试清空所有条件...');

    try {
      await clearAllConditions();
      setFilterPanelFilters({});
      setAdvancedSearchConditions([]);
      setGlobalKeyword('');
      
      addTestResult('✅ 所有条件已清空');
    } catch (error) {
      addTestResult(`❌ 清空失败: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">统一搜索架构测试 V2</h1>
        <Badge variant="outline">数据库: {database}</Badge>
      </div>

      {/* 搜索状态显示 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            当前搜索状态
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <h4 className="font-medium mb-2">搜索条件</h4>
              <p className="text-sm text-gray-600">
                {searchState.conditions.length} 个条件
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">统计缓存</h4>
              <p className="text-sm text-gray-600">
                {searchState.statisticsCache.size} 个字段
              </p>
            </div>
            <div>
              <h4 className="font-medium mb-2">分页信息</h4>
              <p className="text-sm text-gray-600">
                第 {searchState.pagination.page} 页，共 {searchState.pagination.total} 条
              </p>
            </div>
          </div>

          {searchState.isLoading && (
            <div className="flex items-center gap-2 text-blue-600">
              <Loader2 className="h-4 w-4 animate-spin" />
              搜索中...
            </div>
          )}

          {searchState.isStatisticsLoading && (
            <div className="flex items-center gap-2 text-orange-600">
              <Loader2 className="h-4 w-4 animate-spin" />
              统计数据加载中...
            </div>
          )}
        </CardContent>
      </Card>

      {/* 测试按钮 */}
      <Card>
        <CardHeader>
          <CardTitle>功能测试</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <Button 
              onClick={testFilterPanelSearch} 
              disabled={isLoading || !config}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              Filter面板搜索
            </Button>

            <Button 
              onClick={testAdvancedSearch} 
              disabled={isLoading || !config}
              className="flex items-center gap-2"
            >
              <Search className="h-4 w-4" />
              Advanced Search
            </Button>

            <Button 
              onClick={testGlobalSearch} 
              disabled={isLoading || !config}
              className="flex items-center gap-2"
            >
              <Search className="h-4 w-4" />
              全局搜索
            </Button>

            <Button 
              onClick={testStatisticsSharing} 
              disabled={!config}
              variant="outline"
            >
              统计数据共享
            </Button>

            <Button 
              onClick={testClearAll} 
              disabled={isLoading}
              variant="outline"
            >
              清空所有条件
            </Button>

            <Button 
              onClick={() => setTestResults([])} 
              variant="ghost"
            >
              清空日志
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 测试结果 */}
      <Card>
        <CardHeader>
          <CardTitle>测试日志</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
            {testResults.length === 0 ? (
              <p className="text-gray-500">暂无测试结果</p>
            ) : (
              <div className="space-y-1">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono">
                    {result}
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
