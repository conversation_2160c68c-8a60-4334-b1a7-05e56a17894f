"use client";

import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useEffect, useState, Suspense } from "react";
import Navigation from "@/components/Navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, X } from "lucide-react";
import Link from "next/link";
import { useGlobalSearch } from "@/hooks/use-global-search";
import { getDatabaseConfigs } from "@/lib/permissions-client";
import { getPreloadedDatabaseConfigs, preloadAppData } from "@/lib/preloader";


interface DatabaseConfig {
  name: string;
  category: string;
  description: string;
  accessLevel: string;
  icon?: string;
}

// 优化的数据库配置获取函数
async function getOptimizedDatabaseConfigs(): Promise<Record<string, DatabaseConfig>> {
  // 首先尝试从预加载缓存获取
  const preloaded = getPreloadedDatabaseConfigs();
  if (preloaded && Object.keys(preloaded).length > 0) {
    return preloaded;
  }

  // 如果预加载缓存为空，触发预加载并等待
  try {
    await preloadAppData();
    const afterPreload = getPreloadedDatabaseConfigs();
    if (afterPreload && Object.keys(afterPreload).length > 0) {
      return afterPreload;
    }
  } catch (error) {
    console.warn('Preload failed, falling back to direct fetch:', error);
  }

  // 最后回退到直接获取
  return getDatabaseConfigs();
}

function HomePageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const {
    query: searchQuery,
    setQuery: setSearchQuery,
    results,
    isSearching,
    performSearch,
    clearResults
  } = useGlobalSearch();
  const [databaseConfigs, setDatabaseConfigs] = useState<Record<string, DatabaseConfig>>({});
  const [loading, setLoading] = useState(true);
  const [hasInitialized, setHasInitialized] = useState(false);
  const [mounted, setMounted] = useState(false);

  // 首先处理客户端挂载
  useEffect(() => {
    setMounted(true);
  }, []);

  // 获取数据库配置 - 终极解决方案
  useEffect(() => {
    if (!mounted) return; // 只在客户端执行

    let isMounted = true;

    const fetchConfigs = async () => {
      console.log('[HomePage] 客户端开始获取数据库配置...');

      try {
        const response = await fetch('/api/config/databases');
        console.log('[HomePage] API响应状态:', response.status);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const result = await response.json();
        console.log('[HomePage] API响应:', result);

        if (result.success && result.data && isMounted) {
          console.log('[HomePage] 成功获取配置:', Object.keys(result.data).length, '个数据库');
          setDatabaseConfigs(result.data);
        } else if (isMounted) {
          console.warn('[HomePage] API返回失败');
          setDatabaseConfigs({});
        }
      } catch (error) {
        console.error('[HomePage] 配置获取失败:', error);
        if (isMounted) {
          setDatabaseConfigs({});
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    // 立即执行
    fetchConfigs();

    return () => {
      isMounted = false;
    };
  }, [mounted]);

  // 检查URL参数中是否有搜索查询（仅在初始加载时）
  useEffect(() => {
    const urlQuery = searchParams.get('q');
    if (urlQuery && urlQuery.trim() && !hasInitialized) {
      setSearchQuery(urlQuery.trim());
      // 自动执行搜索（仅在从URL加载时）
      performSearch(urlQuery.trim());
      setHasInitialized(true);
    } else if (!urlQuery && !hasInitialized) {
      setHasInitialized(true);
      clearResults(); // 清空之前的搜索结果
    }
  }, [searchParams, setSearchQuery, hasInitialized, performSearch, clearResults]);

  // 处理搜索操作
  const handleSearch = () => {
    if (searchQuery.trim()) {
      // 更新URL参数
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.set('q', searchQuery.trim());
      router.replace(newUrl.toString());

      // 执行搜索
      performSearch();
    } else {
      // 清空搜索
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('q');
      router.replace(newUrl.toString());
      clearResults();
    }
  };

  // Home 页面搜索只负责展示下方结果，不自动导航
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      // prevent form submission / page reload
      e.preventDefault();
      handleSearch();
    }
  };

  // 根据动态获取的配置按类别分组
  const groupedDatabases = Object.entries(databaseConfigs).reduce<Record<string, { code: string; name: string }[]>>((acc, [code, cfg]) => {
    if (!acc[cfg.category]) acc[cfg.category] = [];
    acc[cfg.category].push({ code, name: cfg.name });
    return acc;
  }, {});

  // 将搜索结果转换为map便于快速查找
  const resultCountMap = new Map(results.map(r => [r.database.toLowerCase(), r.count]));

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />

      <main className="max-w-6xl mx-auto pt-16 pb-24">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            DataQuery Pharmaceutical Database
          </h1>

          {/* Search Area */}
          <div className="flex justify-center mb-20 px-4">
            <div className="flex items-center w-full max-w-2xl">
              <div className="relative flex-1">
                <Input
                  type="text" placeholder="Enter product name, company name, application number, registration number, or title for search" className="h-12 text-sm md:text-base pr-16 border-r-0 rounded-r-none" value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={handleKeyPress}
                  showClearButton={false}
                />
                {searchQuery && (
                  <button
                    type="button"
                    onClick={() => setSearchQuery("")}
                    className="absolute right-10 top-1/2 transform -translate-y-1/2 h-4 w-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
                    tabIndex={-1}
                  >
                    <X className="h-4 w-4" />
                    <span className="sr-only">Clear search</span>
                  </button>
                )}
                <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
              <Button
                className="h-12 px-4 md:px-8 rounded-l-none bg-blue-600 hover:bg-blue-700"
                disabled={!searchQuery.trim() || isSearching}
                onClick={handleSearch}
              >
                {isSearching ? 'Searching...' : 'Search'}
              </Button>
            </div>
          </div>
        </div>

        {/* 数据库目录（始终显示） */}
        <div className="px-4 md:px-8 space-y-6 max-w-4xl mx-auto">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading database list...</p>
              <p className="text-sm text-gray-400 mt-2">
                If this takes too long, please check your database connection
              </p>
            </div>
          ) : Object.keys(groupedDatabases).length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-500 mb-4">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v1M7 8h10M7 12h4m1 8l-1-1h1v1z" />
                </svg>
              </div>
              <p className="text-gray-600 mb-2">No database configurations found</p>
              <p className="text-sm text-gray-400">Please check your database connection and configuration</p>
            </div>
          ) : Object.entries(groupedDatabases).map(([category, dbs]) => (
            <div key={category}>
              <div className="flex items-center mb-2 text-lg font-medium text-gray-800">
                <span className="mr-2">{category}</span>
              </div>
              <div className="flex flex-wrap gap-x-6 gap-y-2 pl-2">
                {dbs.map((d) => {
                  const count = searchQuery.trim() ? resultCountMap.get(d.code.toLowerCase()) ?? 0 : undefined;
                  return (
                    <Link
                      key={d.code}
                      href={searchQuery.trim() ? `/data/list/${d.code}?allFields=${encodeURIComponent(searchQuery)}` : `/data/list/${d.code}`}
                      className="text-sm text-blue-600 hover:underline flex items-center gap-1">
                      <span className="w-2 h-2 rounded-full bg-blue-500 inline-block" />
                      {d.name}
                      {count !== undefined ? ` (${count})` : null}
                    </Link>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-50 border-t border-gray-200 py-8">
        <div className="max-w-6xl mx-auto px-8">
          <div className="flex items-center justify-center space-x-6 text-sm text-gray-600">
            <Link href="/about" className="hover:text-blue-600">About Us</Link>
            <span>•</span>
            <Link href="/contact" className="hover:text-blue-600">Contact Us</Link>
            <span>•</span>
            <Link href="/sitemap" className="hover:text-blue-600">Site Map</Link>
          </div>
          <div className="text-center text-xs text-gray-500 mt-4">
            © 2025 DataQuery. All Rights Reserved.
          </div>
        </div>
      </footer>
    </div>
  );
}

export default function HomePage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <main className="max-w-6xl mx-auto pt-16 pb-24">
          <div className="text-center mb-16">
            <h1 className="text-5xl font-bold text-gray-900 mb-6">
              DataQuery Pharmaceutical Database
            </h1>
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading...</p>
            </div>
          </div>
        </main>
      </div>
    }>
      <HomePageContent />
    </Suspense>
  );
}
