'use client';

import React, { useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, ChevronLeft, ChevronRight, ArrowUpDown, ArrowUp, ArrowDown, RotateCcw } from 'lucide-react';
import { useResizableColumns } from '@/hooks/useResizableColumns';
import { ColumnResizer } from '@/components/ColumnResizer';
import { smartFormatDate } from '@/lib/utils';
import { DatabaseFieldConfig } from '@/lib/hooks/useUnifiedSearch';

interface DataTableProps {
  data: any[];
  config: { fields: DatabaseFieldConfig[] } | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
  onPageChange: (direction: 'prev' | 'next') => void;
  onSortChange: (fieldName: string, order: 'asc' | 'desc') => void;
  loading: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export const DataTable: React.FC<DataTableProps> = ({
  data,
  config,
  pagination,
  onPageChange,
  onSortChange,
  loading,
  sortBy,
  sortOrder
}) => {
  // Get visible fields for table headers
  const tableHeaders = useMemo(() => {
    if (!config?.fields) return [];
    return config.fields
      .filter(f => f.isVisible)
      .sort((a, b) => (a as any).listOrder - (b as any).listOrder);
  }, [config]);

  // Initialize resizable columns
  const initialColumns = useMemo(() =>
    tableHeaders.map(header => ({
      fieldName: header.fieldName,
      fieldType: header.fieldType,
      displayName: header.displayName
    })), [tableHeaders]);

  const resizableColumns = useResizableColumns({
    initialColumns
  });

  // Handle sorting
  const handleSort = (fieldName: string) => {
    if (!tableHeaders.find(h => h.fieldName === fieldName)?.isSortable) return;
    
    let newOrder: 'asc' | 'desc' = 'asc';
    if (sortBy === fieldName && sortOrder === 'asc') {
      newOrder = 'desc';
    }
    onSortChange(fieldName, newOrder);
  };

  // Handle row click (placeholder for future implementation)
  const handleRowClick = (row: any) => {
    console.log('Row clicked:', row);
    // TODO: Implement row detail view or other actions
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading...</span>
      </div>
    );
  }

  if (!config || tableHeaders.length === 0) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="text-center">
          <p className="text-gray-500">No configuration available</p>
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="text-center">
          <p className="text-gray-500">No data found</p>
          <p className="text-sm text-gray-400 mt-1">Try adjusting your search criteria</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Table Header */}
      <div className={`bg-gray-50 border-b border-gray-200 ${resizableColumns.isResizing ? 'resizing' : ''}`}>
        {/* Column width reset button */}
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 z-40">
          <Button
            variant="ghost"
            size="sm"
            onClick={resizableColumns.resetColumnWidths}
            className="h-6 w-6 p-0 hover:bg-gray-200"
            title="Reset column widths"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
        
        <div className="overflow-x-auto">
          <div className="min-w-max">
            <div className="px-4 py-3">
              <div className="flex text-sm font-medium text-gray-700">
                {tableHeaders.map((header, index) => {
                  const isSortable = header.isSortable;
                  const isCurrentSort = sortBy === header.fieldName;
                  const isLast = index === tableHeaders.length - 1;
                  const columnStyle = resizableColumns.getColumnStyle(header.fieldName);

                  return (
                    <div
                      key={header.fieldName}
                      className="relative flex items-center"
                      style={columnStyle}
                    >
                      <div
                        className={`flex items-center gap-1 ${
                          isSortable ? 'cursor-pointer hover:text-gray-900' : ''
                        }`}
                        onClick={() => isSortable && handleSort(header.fieldName)}
                      >
                        <span className="truncate">{header.displayName}</span>
                        {isSortable && (
                          <div className="flex-shrink-0">
                            {isCurrentSort ? (
                              sortOrder === 'asc' ? (
                                <ArrowUp className="h-3 w-3" />
                              ) : (
                                <ArrowDown className="h-3 w-3" />
                              )
                            ) : (
                              <ArrowUpDown className="h-3 w-3 opacity-50" />
                            )}
                          </div>
                        )}
                      </div>

                      {/* Column resizer - not shown on last column */}
                      {!isLast && (
                        <ColumnResizer
                          fieldName={header.fieldName}
                          isResizing={resizableColumns.resizingColumn === header.fieldName}
                          isActive={resizableColumns.resizingColumn === header.fieldName}
                          onMouseDown={resizableColumns.handleMouseDown}
                        />
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Table Body */}
      <div className="overflow-x-auto">
        <div className="min-w-max">
          {data.map((row, index) => (
            <div
              key={row.id ? String(row.id) : `row-${index}`}
              className="px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors"
              onClick={() => handleRowClick(row)}
            >
              <div className="flex text-sm min-w-max">
                {tableHeaders.map((header) => {
                  const columnStyle = resizableColumns.getColumnStyle(header.fieldName);
                  const cellValue = smartFormatDate(
                    row[header.fieldName],
                    { fieldType: header.fieldType, fieldName: header.fieldName }
                  );

                  return (
                    <div
                      key={header.fieldName}
                      className="truncate text-gray-900"
                      style={columnStyle}
                      title={String(cellValue || '')}
                    >
                      {cellValue || '-'}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Pagination Footer */}
      <div className="flex justify-between items-center p-4 bg-white border-t border-gray-200">
        <span className="text-sm text-gray-600">
          Showing {(pagination.page - 1) * pagination.limit + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total.toLocaleString()} records
        </span>
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange('prev')}
            disabled={pagination.page <= 1 || loading}
          >
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Button>
          <span className="text-sm text-gray-600">
            Page {pagination.page}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange('next')}
            disabled={pagination.page * pagination.limit >= pagination.total || loading}
          >
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DataTable;
